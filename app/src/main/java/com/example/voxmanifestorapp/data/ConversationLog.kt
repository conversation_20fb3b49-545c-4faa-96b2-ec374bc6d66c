package com.example.voxmanifestorapp.data

import android.util.Log
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.voxmanifestorapp.ui.agent.ConversationEntry
import com.example.voxmanifestorapp.ui.agent.ConversationPhase
import com.example.voxmanifestorapp.ui.agent.Speaker
import kotlinx.serialization.json.Json
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Room entity for persistent storage of conversation history.
 * This provides a dual-layer memory system, storing both raw conversations
 * and metadata for structured retrieval.
 */
@Entity(
    tableName = "conversation_logs",
    indices = [
        Index(value = ["sessionId"], name = "index_conversation_logs_sessionId"),
        Index(value = ["wishId"], name = "index_conversation_logs_wishId")
    ]
)
data class ConversationLogEntry(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // Session grouping and sorting
    val sessionId: String,          // UUID for grouping conversation turns into sessions
    val timestamp: Long,            // For sorting and recency tracking
    
    // Content metadata
    val wishId: Int,                // Associated wish (-1 if general/no specific wish)
    val phase: String,              // The conversation phase (maps to ConversationPhase)
    val speaker: String,            // "Agent" or "User"
    val content: String,            // The actual text
    
    // Visibility control
    val isVisible: Boolean,         // Whether to show in UI history
    
    // NEW: Metadata storage for themes and other structured data
    val metadata: String? = null    // JSON serialized metadata
)

/**
 * Converts a ConversationLogEntry to a less detailed ConversationEntry
 * for in-memory representation in the UI.
 * Now includes metadata restoration from database.
 */
fun ConversationLogEntry.toConversationEntry(): ConversationEntry {
    val conversationPhase = try {
        ConversationPhase.valueOf(phase)
    } catch (e: IllegalArgumentException) {
        ConversationPhase.CHECK_IN // Fallback to a default phase
    }

    // Restore metadata from JSON
    val restoredMetadata = metadata?.let { jsonString ->
        try {
            // Use proper JSON parsing for the metadata
            val json = Json { 
                ignoreUnknownKeys = true 
                isLenient = true
                coerceInputValues = true
            }
            json.decodeFromString<Map<String, String>>(jsonString)
        } catch (e: Exception) {
            Log.w("ConversationLog", "Failed to parse metadata from database: ${e.message}")
            Log.w("ConversationLog", "Problematic JSON: $jsonString")
            
            // Try to handle old double-escaped format as fallback
            try {
                Log.d("ConversationLog", "Attempting to parse old double-escaped format...")
                // The old format has double-escaped quotes, so we need to unescape them
                val unescapedJson = jsonString
                    .replace("\\\"", "\"")  // Unescape double quotes
                    .replace("\\\\", "\\")   // Unescape backslashes
                
                Log.d("ConversationLog", "Unescaped JSON: ${unescapedJson.take(200)}...")
                
                val json = Json { 
                    ignoreUnknownKeys = true 
                    isLenient = true
                    coerceInputValues = true
                }
                json.decodeFromString<Map<String, String>>(unescapedJson)
            } catch (fallbackException: Exception) {
                Log.w("ConversationLog", "Fallback parsing also failed: ${fallbackException.message}")
                emptyMap()
            }
        }
    } ?: emptyMap()

    return ConversationEntry(
        speaker = when (speaker) {
            "Agent" -> Speaker.Agent
            "User" -> Speaker.User
            "SessionName" -> Speaker.SessionName
            else -> Speaker.User  // Fallback
        },
        content = content,
        timestamp = timestamp,
        phase = conversationPhase,
        isVisible = isVisible,
        metadata = restoredMetadata  // ← RESTORE METADATA
    )
} 

/**
 * Simple data class to hold metadata about a conversation session.
 * Used for the conversation history session cards.
 */
data class SessionMetadata(
    val sessionId: String,
    val startTimestamp: Long
) 