# VoxManifestorApp Development Workflow

## Quick Command: "Let's Package This Version"

When the user says **"let's package this version"**, execute this complete version packaging process:

**Context**: This assumes you're on a feature branch with completed work that needs to be packaged as a new version.

### Version Packaging Process
1. **Commit feature branch work** - Save all current changes to feature branch
2. **Merge to main branch** - Get feature work into main for packaging
3. **Update CHANGELOG.md** - Add/organize changelog entries for the current version
4. **Stage and commit version changes** - Prepare for vpush
5. **Run vpush script** - Handle version tagging and remote push

### Commands to Execute
```bash
# Step 1: Commit current feature work (you're on feature branch)
git add -A
git commit -m "Complete [feature-name] implementation"

# Step 2: Merge to main branch
git checkout main && git pull origin main
git merge [current-feature-branch-name]

# Step 3: Update CHANGELOG.md with version details

# Step 4: Commit version changes
git add -A
git commit -m "Version X.Y.Z - [Brief description of major changes]"

# Step 5: Package and tag the version
./vpush X.Y.Z "[Brief change description]"
```

### Strategic Role in Version Packaging
- **Suggest appropriate version number** (major.minor.patch based on changes)
- **Update CHANGELOG.md** with organized entries following the established format
- **Coordinate the complete release process** ensuring all steps are properly executed
- **Ensure proper branch management** - commit feature work before merging to main

### Common Issues and Solutions
- **"Working directory is not clean"**: Complete Step 1 first - commit your feature work
- **"vpush can only be run on main/master branch"**: Ensure you completed Step 2 - merge to main
- **Uncommitted changes**: Always commit feature work first, then proceed with version packaging

---

## Quick Start: Feature Development Process

### 1. Start Feature
```bash
git checkout main && git pull origin main
git checkout -b feature/your-feature-name  # or bugfix/fix-name
```

### 2. Develop & Test
- Implement changes following MVVM architecture
- Test on device manually

!! NOTE !! We are not currently doing pull requests - this can be ignored for now
<!--
### 3. Create Pull Request
- **DO NOT** update VERSION file (saved for release step)
- Update CHANGELOG.md with your changes (but no version number)
- Create PR to `main` branch
- Include testing checklist and architecture impact
-->

### 4. Review & Merge
- Code review and approval required
- Merge to `main` (squash and merge preferred)
- Delete feature branch

### 5. Changelog Updates
- **Update CHANGELOG.md during development**: Add concise entries as you implement changes
- **Keep entries brief but sufficient**: Focus on what changed, not how it was implemented
- **Use consistent format**: Follow existing changelog structure with Changed/Added/Fixed sections
- **Include technical context**: Mention architectural impacts and key technical improvements
- **Version numbers**: Only add version numbers during release step, not during development
- **Structure**: New detailed entries go at the top, right after the Version History section
- **Version History**: Add one-line summaries to the bottom of the chronological list (newest at bottom)
- **Format**: Use `## [X.Y.Z] - Description (YYYY-MM-DD)` format for detailed entries

### 6. Release (Main Branch Only)
```bash
git checkout main && git pull origin main
# Update CHANGELOG.md with version number if needed
# Stage and commit all changes (vpush requires clean working directory)
git add -A
git commit -m "Version X.Y.Z - Brief description with changelog updates"
# REQUIRED: Run vpush to tag version and complete release
./vpush X.Y.Z "Brief change description"
```

**⚠️ RELEASE REQUIRES BOTH STEPS:** The commit AND vpush are both required for a complete release. The vpush script handles version tagging and push to remote.

**⚠️ IMPORTANT:** Never update VERSION file on feature branches! The vpush script handles version updates and should only run on main branch after merge.

**📝 CHANGELOG GUIDELINES:** Update CHANGELOG.md during development with concise entries. Focus on what changed rather than implementation details. Only add version numbers during the release step.

**Version Numbering:**
- **Major** (X.0.0): Breaking changes, major features
- **Minor** (2.X.0): New features, enhancements  
- **Patch** (2.10.X): Bug fixes, small improvements

**Changelog Requirements:**
- **Version Title Format**: `## [X.Y.Z] - Description (YYYY-MM-DD)`
  - Example: `## [2.11.0] - ConversationAgent Modular Refactoring (2025-06-30)`
- **Version History Summary**: Add one-line summary with date in chronological order
  - Example: `30/06/2025   v2.11.0 TransitionChain upgrade with enhanced two-chain architecture`
- **Detailed Section**: Include Added/Changed/Fixed subsections with technical details
- **Date Source**: Use actual git commit/tag dates for accuracy




---

## Detailed Documentation

## Branch Types

#### Main Branches
- **`main`**: Production-ready code. Protected branch with all releases.
- **`develop`** (if used): Integration branch for features before merging to main.

#### Feature Branches
- **Format**: `feature/issue-number-description` or `feature/description`
- **Examples**: 
  - `feature/25-pr-workflow`
  - `feature/voice-recognition-improvements`

#### Bug Fix Branches
- **Format**: `bugfix/issue-number-description` or `bugfix/description`
- **Examples**:
  - `bugfix/123-conversation-agent-memory-leak`
  - `bugfix/speech-recognition-timeout`

#### Hotfix Branches
- **Format**: `hotfix/issue-number-description` or `hotfix/description`
- **Examples**:
  - `hotfix/critical-crash-android-14`
  - `hotfix/gemini-api-authentication`

#### Refactor Branches
- **Format**: `refactor/description`
- **Examples**:
  - `refactor/conversation-agent-decomposition`
  - `refactor/agent-ui-components-split`

### Branch Lifecycle
1. Create feature branch from `main`
2. Develop feature with regular commits
3. Test thoroughly (unit, integration, device testing)
4. Create pull request to `main`
5. Code review and approval
6. Merge and delete feature branch

## Feature Development Process

### 1. Issue Creation and Planning
- Create GitHub issue with clear description
- Add appropriate labels (feature, bug, enhancement, etc.)
- Assign to project board column
- Estimate effort and priority

### 2. Branch Creation
```bash
# Ensure you're on main and up to date
git checkout main
git pull origin main

# Create and switch to feature branch
git checkout -b feature/your-feature-name
```

### 3. Development Guidelines
- Follow MVVM architecture patterns established in the codebase
- Maintain reactive state management with StateFlow
- Consider impact on the 7-phase Core Loop state machine
- Follow existing code style and conventions
- Keep commits atomic and well-documented
- Write meaningful commit messages

### 4. Architecture Considerations
When making changes, consider the established architecture:
- **UI Changes**: Modify Composable → Update ViewModel → Update Repository if needed
- **Data Model Changes**: Update Entity → Update DAO → Update Repository → Update ViewModel → Update UI
- **Agent Logic Changes**: Update ConversationAgent → Consider AgentCortex state impact → Update UI components
- **Voice Processing Changes**: Update Repository → Update ViewModel → Coordinate with agent system

## Pull Request Process

### PR Template Checklist
1. **Summary**: Brief overview of changes
2. **Changes Made**: Detailed list of modifications
3. **Testing Performed**: Comprehensive testing checklist
4. **Architecture Impact**: Effects on core systems
5. **Screenshots/Videos**: For UI changes, especially voice features
6. **Additional Notes**: Any relevant context or considerations

### ⚠️ PR Creation Rules
- **NEVER include VERSION file changes** in feature branch PRs
- **CHANGELOG.md**: Add your changes without version numbers
- **Version bumps**: Only done during release step with vpush script
- **Branch validation**: vpush script prevents running on non-main branches
- **GitHub authorship**: Do not add Claude authorship annotations to GitHub messages (PRs, commits, issues)


## Testing Requirements

### Pre-PR Testing Checklist

#### Unit Tests
- [ ] All new code has appropriate unit tests
- [ ] Existing tests pass: `./gradlew test`
- [ ] Code coverage maintained or improved
- [ ] Mock dependencies properly for ViewModels and Repositories

#### Integration Tests
- [ ] Database operations tested with Room
- [ ] Repository layer integration tested
- [ ] ViewModel state management tested
- [ ] Agent system state transitions tested

#### Android Instrumentation Tests
- [ ] UI components tested with Compose UI tests
- [ ] Navigation flows tested
- [ ] Database migrations tested
- [ ] Run tests: `./gradlew connectedAndroidTest`

#### Manual Testing
- [ ] Feature works on physical Android device
- [ ] Feature tested on emulator (API 24+)
- [ ] Voice input/output functionality verified
- [ ] App doesn't crash during normal usage
- [ ] Performance acceptable (no ANRs, memory leaks)

#### Build and Lint Checks
- [ ] Project builds successfully: `./gradlew build`
- [ ] No lint errors: `./gradlew lint`
- [ ] No KSP compilation errors
- [ ] ProGuard rules work for release builds

## Code Review Process

### Review Checklist
- [ ] Code follows established patterns and conventions
- [ ] No obvious bugs or security issues
- [ ] Performance implications considered
- [ ] Error handling implemented appropriately
- [ ] Memory leaks avoided (especially for Android lifecycle)
- [ ] StateFlow usage follows reactive patterns
- [ ] Voice processing changes don't break existing functionality

### Review Focus Areas
1. **Code Quality**: Readability, maintainability, adherence to patterns
2. **Architecture**: Proper MVVM implementation, state management
3. **Performance**: Efficient algorithms, proper coroutine usage
4. **Security**: No API keys or sensitive data in code
5. **Testing**: Adequate test coverage and quality
6. **Android Best Practices**: Lifecycle handling, UI thread usage

## Merge Criteria

### Automated Checks (Must Pass)
- [ ] All GitHub Actions/CI checks pass
- [ ] Build succeeds for all variants (debug/release)
- [ ] Unit tests pass
- [ ] Instrumentation tests pass
- [ ] Lint checks pass
- [ ] No merge conflicts with target branch

### Manual Verification (Must Complete)
- [ ] Code review approved by required reviewers
- [ ] Feature tested on actual Android device
- [ ] Voice functionality verified if applicable
- [ ] No regression in existing features
- [ ] Documentation updated if needed
- [ ] CHANGELOG.md updated for significant changes
- [ ] **VERSION file NOT modified** in PR (reserved for release step)

### Merge Strategy
- **Default**: Squash and merge for feature branches
- **Preserve History**: Merge commit for complex multi-author features
- **Rebase**: For small, clean commits that benefit from linear history

### Post-Merge Actions
1. Delete feature branch immediately
2. Update related issues/project board
3. Monitor for any post-merge issues
4. Deploy to staging/testing environment if applicable

## Branch Protection Rules

### Recommended Settings for `main` Branch
- **Require pull request reviews before merging**: ✅
  - Required approving reviews: 1
  - Dismiss stale reviews when new commits are pushed: ✅
- **Require status checks to pass before merging**: ✅
  - Build checks
  - Test suites
  - Lint checks
- **Require conversation resolution before merging**: ✅
- **Require signed commits**: Optional but recommended
- **Require linear history**: Optional (consider for cleaner history)
- **Delete head branches**: ✅ (automatically clean up merged branches)

## Android Guidelines

### Build System
- Use Gradle Kotlin DSL (`.gradle.kts`)
- Maintain compatibility with Android Gradle Plugin versions
- Keep KSP version aligned with Kotlin version
- Update dependencies regularly but test thoroughly

### Performance Considerations
- Monitor memory usage, especially in ConversationAgent (131KB file)
- Avoid memory leaks in ViewModel and Repository layers
- Use coroutines properly for background operations
- Test on lower-end devices (API 24+)

### Architecture Components
- Follow MVVM with StateFlow for reactive UI
- Use Room database migrations properly
- Implement proper dependency injection through AppContainer
- Maintain clean separation between UI, ViewModel, and Repository layers

### Known Issues to Watch
- **ConversationAgent.kt decomposition**: Monitor file size (currently 131KB)
- **AgentUiComponents.kt**: Consider splitting if growing beyond current size
- **Check-In State Synchronization**: Test AgentCortex and DialogueChain integration
- **KSP Version Compatibility**: Upgrade Kotlin version as needed

## Voice Feature Testing

### Google Cloud AI Integration
- Test Google Cloud Speech-to-Text functionality
- Verify Google Cloud Text-to-Speech output quality
- Test Gemini AI response generation and parsing
- Validate API key security and rotation

### Voice User Experience
- Test in different audio environments (quiet, noisy)
- Verify voice commands are recognized accurately
- Test interruption and resumption of voice interactions
- Validate audio feedback and response timing

### Edge Cases
- Test network connectivity issues
- Verify graceful handling of API rate limits
- Test microphone permission handling
- Validate behavior with poor audio quality

## Troubleshooting

### Common Build Issues
- **KSP compilation errors**: Check Kotlin/KSP version compatibility
- **Google Services errors**: Verify google-services.json is present
- **API key issues**: Check secrets.properties and environment variables

### Release Issues
- **vpush "Working directory is not clean" error**: 
  - Run `git status` to see uncommitted changes
  - Stage all changes: `git add -A`
  - Commit changes: `git commit -m "Version description"`
  - Then run vpush script
- **vpush not found**: Ensure you're in the project root directory

### Testing Issues
- **Device testing fails**: Ensure USB debugging enabled and device connected
- **Emulator issues**: Use API 24+ emulator with Google APIs
- **Voice tests fail**: Check microphone permissions and audio setup

### Development Environment
- **Android Studio**: Use latest stable version
- **Java/Kotlin**: Maintain version compatibility
- **Gradle**: Use wrapper for consistent builds across team

### Performance Debugging
- Use Android Studio Profiler for memory/CPU analysis
- Monitor app startup time and responsiveness
- Check for ANRs (Application Not Responding) issues
- Profile voice processing pipeline performance

---

## Quick Reference

### Common Commands
```bash
# Build and test
./gradlew build
./gradlew test
./gradlew connectedAndroidTest
./gradlew lint

# Branch management
git checkout main
git pull origin main
git checkout -b feature/your-feature
git push -u origin feature/your-feature

# Create PR (GitHub CLI)
gh pr create --title "feat: your feature" --body "Description of changes"
```

### Key Files to Monitor
- `ConversationAgent.kt` (131KB - refactoring priority)
- `AgentUiComponents.kt` (43KB - decomposition candidate)
- `BrainService.kt` (33KB - AI integration)
- `VoiceRecognitionRepository.kt` (21KB - speech processing)

### Integration Points
- `AppContainer.kt`: Dependency injection
- `AgentCortex.kt`: Central agent state management
- `MainViewModel.kt`: UI coordination
- `BrainService.kt`: AI capabilities

This workflow ensures high code quality, thorough testing, and smooth collaboration for VoxManifestorApp development.
